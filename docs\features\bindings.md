# Bindings

Bindings are components that create reactive connections between [Value Stores](value-stores.md) and Unity UI elements. They automatically update UI properties when the underlying data changes, enabling a data-driven approach to UI development.

## Overview

Bindings eliminate the need to manually update UI elements when data changes. Instead of writing code to sync UI with your data, you simply connect a Value Store to a UI component through a binding, and the UI will automatically reflect any changes to the data.

All binding components:
- Execute in Edit Mode for immediate visual feedback
- Automatically subscribe to Value Store changes
- Handle cleanup when disabled or destroyed
- Support real-time updates in the Unity Inspector

## Available Bindings

Soup provides several built-in binding components for common UI scenarios:

### GraphicColorBinding

Binds a [ColorStore](value-stores.md#colorstore) to any UI Graphic component, including Image, RawImage, Text, and TextMeshPro components.

**Requirements:**
- Target GameObject must have a `Graphic` component (Image, Text, TMP_Text, etc.)

**Usage:**
1. Add the `GraphicColorBinding` component to a GameObject with a Graphic component
2. Assign a ColorStore asset to the `Color Store` field
3. The Graphic's color will automatically update when the ColorStore value changes

**Menu Path:** `Add Component > Soup > Bindings > Graphic - Color Binding`

**Supported Components:**
- UI Image
- UI RawImage
- UI Text (Legacy)
- TextMeshPro - Text (UI)
- Any component that inherits from `Graphic`

```csharp
// Example: Changing color through code will automatically update the UI
[SerializeField] private ColorStore _buttonColor;

void ChangeButtonColor()
{
    _buttonColor.Value = Color.red; // UI automatically updates
}
```

### TMP_TextContentBinding

Binds a [StringStore](value-stores.md#stringstore) to a TextMeshPro text component's content.

**Requirements:**
- Target GameObject must have a `TMP_Text` component

**Usage:**
1. Add the `TMP_TextContentBinding` component to a GameObject with a TMP_Text component
2. Assign a StringStore asset to the `String Store` field
3. The text content will automatically update when the StringStore value changes

**Menu Path:** `Add Component > Soup > Bindings > TextMeshPro - Text Content Binding`

```csharp
// Example: Dynamic text content updates
[SerializeField] private StringStore _playerName;

void UpdatePlayerName(string newName)
{
    _playerName.Value = newName; // Text content automatically updates
}
```

### TMP_TextFontSizeBinding

Binds a [FloatStore](value-stores.md#floatstore) to a TextMeshPro text component's font size.

**Requirements:**
- Target GameObject must have a `TMP_Text` component

**Usage:**
1. Add the `TMP_TextFontSizeBinding` component to a GameObject with a TMP_Text component
2. Assign a FloatStore asset to the `Font Size Store` field
3. The text font size will automatically update when the FloatStore value changes

**Menu Path:** `Add Component > Soup > Bindings > TextMeshPro - Font Size Binding`

```csharp
// Example: Dynamic font size adjustment
[SerializeField] private FloatStore _fontSize;

void IncreaseFontSize()
{
    _fontSize.Value += 2f; // Text size automatically updates
}
```

### GenericPropertyBinding

Binds any [ValueStore](value-stores.md) to a TextMeshPro text component, with the ability to display either the direct value or any public property/field of the stored value. This is the most flexible binding component, supporting custom formatting and complex data types.

**Requirements:**
- Target GameObject must have a `TMP_Text` component

**Usage:**
1. Add the `GenericPropertyBinding` component to a GameObject with a TMP_Text component
2. Assign any ValueStore asset to the `Value Store` field
3. Select which property to display from the dropdown (or leave as direct value)
4. Optionally customize the `Format String` for value display
5. The text content will automatically update when the ValueStore or selected property changes

**Menu Path:** `Add Component > Soup > Bindings > Generic Property Binding`

**Key Features:**
- **Direct Value Binding**: Display the ValueStore's value directly
- **Property Selection**: Choose any public property or field from the stored value
- **Custom Formatting**: Use format strings to control how values are displayed
- **Type Flexibility**: Works with any ValueStore type, including custom types
- **Error Handling**: Displays helpful error messages for invalid configurations

**Property Selection:**
The component automatically discovers all public properties and fields of the ValueStore's type:

```csharp
// For a PlayerState with properties: Health, Level, Position
public record PlayerState(int Health, int Level, Vector3 Position);

```

The dropdown will show:
- Value (Direct Binding) - displays the entire PlayerState using its ToString() method
- Health - displays just the Health value
- Level - displays just the Level value
- Position - displays the Position vector

**Format String Examples:**
```csharp
// Basic formatting
"{0}" // Default - displays value as-is

// Empty formatting
"" // Default - displays value as-is

// Numeric formatting
"Score: {0:N0}" // "Score: 1,234"
"Health: {0:F1}" // "Health: 85.5"

// Custom text
"Level {0}" // "Level 5"
"Position: {0}" // "Position: (1.0, 2.0, 3.0)"

// Complex formatting for multiple display scenarios
"Player is at level {0}" // When binding to Level property
```

## Common Use Cases

### Theme Systems

Bindings are perfect for implementing theme systems where colors and sizes need to change globally:

```csharp
// Create ColorStore assets for your theme
public ColorStore PrimaryColor;
public ColorStore SecondaryColor;
public ColorStore TextColor;

// Bind UI elements to these stores
// When theme changes, all bound UI elements update automatically
```

### Accessibility Features

Use bindings to implement accessibility features like font size scaling:

```csharp
// Single FloatStore controls font size across the entire UI
[SerializeField] private FloatStore _globalFontSizeMultiplier;

// All text elements bound to this store will scale together
```

### Dynamic UI States

Create responsive UI that reacts to game state changes:

```csharp
// Health bar color changes based on player health
[SerializeField] private ColorStore _healthBarColor;

void UpdateHealthColor(float healthPercentage)
{
    _healthBarColor.Value = Color.Lerp(Color.red, Color.green, healthPercentage);
    // All UI elements with GraphicColorBinding bound to this store update automatically
}
```

### Dynamic Text Content

Use text content bindings to create responsive text that updates based on game data:

```csharp
// Player stats that update in real-time
[SerializeField] private StringStore _playerScore;
[SerializeField] private StringStore _playerLevel;
[SerializeField] private StringStore _currentWeapon;

void UpdatePlayerStats(int score, int level, string weapon)
{
    _playerScore.Value = $"Score: {score:N0}";
    _playerLevel.Value = $"Level {level}";
    _currentWeapon.Value = weapon;
    // All UI text elements bound to these stores update automatically
}
```