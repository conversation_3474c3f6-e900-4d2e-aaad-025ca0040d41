using System;
using TMPro;

namespace Soup
{
    public static class BindingExtensions
    {
        public static EventSubscription Bind<T, TSelection>(
            this TMP_Text text,
            ValueStore<T> store,
            Func<T, TSelection> selector = null,
            string formatString = null)
        {
            selector ??= value => (TSelection)(object)value;
            formatString ??= "{0}";

            return store.AddListener(value => text.text = string.Format(formatString, selector(value)));
        }
        
        public static EventSubscription Bind<T>(
            this TMP_Text text,
            ValueStore<T> store,
            string formatString = null)
        {
            formatString ??= "{0}";

            return store.AddListener(value => text.text = string.Format(formatString, value));
        }
    }
}